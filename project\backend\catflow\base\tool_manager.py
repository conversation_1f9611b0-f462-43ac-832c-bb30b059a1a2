from typing import Optional, List, Union, Dict, Any
from .llm_tool import Base<PERSON>MTool,LLMToolset
from .llm_mcp_tool import LLM_MCPTool
from .llm_function_tool import LLM_FunctionTool
from .llm_manager import LLMManager
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.base_toolset import BaseToolset
from google.adk.tools.tool_context import ToolContext
import os
import json
import asyncio

class ToolManager(BaseToolset):
    """
    工具管理器：
    统一管理不同类型的工具，支持动态加载和创建
    """
    def __init__(self, llm_manager: LLMManager, config_path: Optional[str] = None):
        """
        初始化工具管理器
        :param llm_manager: LLM管理器实例
        :param config_path: 工具配置文件路径，如果为None则使用默认路径
        """
        super().__init__()
        self.llm_manager = llm_manager
        self.tools: List[Union[BaseTool, BaseToolset]] = []
        # 在初始化时加载工具配置
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'tool_config.json')
        self.load_tools_from_config(config_path)
        
    def load_tools_from_config(self, config_path: Optional[str] = None) -> None:
        """
        从配置文件加载工具
        :param config_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'tool_config.json')
            
        print(f"加载工具配置文件: {config_path}")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        if 'tools' not in config or not isinstance(config['tools'], dict):
            raise ValueError("配置文件格式错误：缺少'tools'字段或类型不为dict")
            
        for tool_id, tool_config in config['tools'].items():
            try:
                tool_type = tool_config.get('type', 'llm')  # 默认为llm类型
                
                if tool_type == 'llm':
                    # 创建LLM工具
                    tool = BaseLLMTool(
                        name=tool_config['name'],
                        llm_manager=self.llm_manager,
                        llm_name=tool_config['llm_name'],
                        instruction=tool_config['instruction'],
                        json_schema=tool_config['json_schema'],
                        descriptions=tool_config.get('descriptions'),
                        temperature=tool_config.get('temperature', 0.3)
                    )
                    self.tools.append(tool)
                    print(f"成功加载LLM工具: {tool_id}")
                elif tool_type == 'mcp':
                    # 创建MCP工具
                    tool = LLM_MCPTool(
                        name=tool_config['name'],
                        llm_manager=self.llm_manager,
                        llm_name=tool_config.get('llm_name'),
                        instruction=tool_config.get('instruction', ''),
                        connection_type=tool_config['connection_type'],
                        connection_params=tool_config['connection_params'],
                    )
                    self.tools.append(tool)
                    print(f"成功加载MCP工具: {tool_id}")
                elif tool_type == 'function':
                    # 创建网页API工具
                    tool = LLM_FunctionTool(
                        name=tool_config['name'],
                        llm_manager=self.llm_manager,
                        llm_name=tool_config.get('llm_name'),
                        instruction=tool_config.get('instruction', ''),
                        api_configs=tool_config['api_configs'],
                        temperature=tool_config.get('temperature', 0.3)
                    )
                    self.tools.append(tool)
                    print(f"成功加载网页API工具: {tool_id}")
            except Exception as e:
                print(f"加载工具 {tool_id} 时出错: {str(e)}")

    async def get_tools(self, readonly_context: Optional[ToolContext] = None) -> List[BaseTool]:
        """
        获取所有可用的工具
        :param readonly_context: 工具上下文
        :return: 工具列表
        """
        all_tools = []
        for tool in self.tools:
            if isinstance(tool, LLM_MCPTool):
                # LLM_MCPTool现在返回包装后的工具列表
                wrapped_tools = await tool.get_tools(readonly_context)
                #print(f"LLM_MCP工具 {tool.name} 包含包装工具: {[t.name for t in wrapped_tools]}")
                all_tools.extend(wrapped_tools)
            elif isinstance(tool, LLM_FunctionTool):
                # LLM_FunctionTool返回包装后的API工具列表
                wrapped_tools = await tool.get_tools(readonly_context)
                #print(f"LLM_Function工具 {tool.name} 包含包装工具: {[t.name for t in wrapped_tools]}")
                all_tools.extend(wrapped_tools)
            elif isinstance(tool, BaseToolset):
                tools = await tool.get_tools(readonly_context)
                #print(f"工具集 {tool.name} 包含工具: {[t.name for t in tools]}")
                all_tools.extend(tools)
            else:
                all_tools.append(tool)
        return all_tools

    async def list_tools(self) -> List[str]:
        """
        列出所有可用的工具
        :return: 工具名称列表
        """
        tools = await self.get_tools(None)
        return [tool.name for tool in tools]

    async def close(self) -> None:
        """关闭所有工具，清理资源"""
        for tool in self.tools:
            try:
                if isinstance(tool, LLM_MCPTool):
                    await tool.close()
                elif isinstance(tool, LLM_FunctionTool):
                    await tool.close()
                elif isinstance(tool, BaseToolset):
                    await tool.close()
            except Exception as e:
                print(f"关闭工具时出错: {str(e)}")
        self.tools.clear()