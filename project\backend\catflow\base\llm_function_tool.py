import json
import asyncio
import time
from typing import Dict, Any, Optional, List, Union, Sequence
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.base_toolset import BaseToolset
from google.adk.tools.tool_context import ToolContext
from google.genai import types
import requests
import aiohttp
from .llm_manager import LLMManager

class FunctionAPITool(BaseTool):
    """
    网页API函数工具类：
    实现对网页API的直接调用功能
    """
    def __init__(self, 
                 name: str,
                 description: str,
                 api_config: Dict[str, Any],
                 json_schema: Optional[Dict[str, Any]] = None):
        """
        初始化网页API工具
        :param name: 工具名称
        :param description: 工具描述
        :param api_config: API配置信息，包含url、method、headers等
        :param json_schema: 参数的JSON Schema定义
        """
        super().__init__(name=name, description=description)
        self.api_config = api_config
        self.json_schema = json_schema or {}
        
        # 验证必需的API配置
        if 'url' not in api_config:
            raise ValueError("API配置必须包含'url'字段")
        
        self.url = api_config['url']
        self.method = api_config.get('method', 'GET').upper()
        self.headers = api_config.get('headers', {})
        self.timeout = api_config.get('timeout', 30)
        self.auth = api_config.get('auth', None)

    def _get_declaration(self) -> Optional[types.FunctionDeclaration]:
        """
        获取Google Gemini API的FunctionDeclaration格式
        :return: FunctionDeclaration对象
        """
        try:
            if self.json_schema:
                parameters_schema = self._convert_json_schema_to_gemini_schema(self.json_schema)
            else:
                # 默认参数schema
                parameters_schema = types.Schema(
                    type=types.Type.OBJECT,
                    properties={
                        "params": types.Schema(
                            type=types.Type.OBJECT,
                            description="API请求参数"
                        )
                    }
                )
            
            return types.FunctionDeclaration(
                name=self.name,
                description=self.description,
                parameters=parameters_schema
            )
        except Exception as e:
            print(f"创建函数声明时出错: {str(e)}")
            return None

    def _convert_json_schema_to_gemini_schema(self, schema: Dict[str, Any]) -> types.Schema:
        """
        将JSON Schema转换为Google Gemini API的Schema格式
        :param schema: JSON Schema字典
        :return: types.Schema对象
        """
        type_mapping = {
            "object": types.Type.OBJECT,
            "string": types.Type.STRING,
            "number": types.Type.NUMBER,
            "integer": types.Type.INTEGER,
            "boolean": types.Type.BOOLEAN,
            "array": types.Type.ARRAY
        }
        
        schema_type = schema.get("type", "object")
        gemini_type = type_mapping.get(schema_type, types.Type.OBJECT)
        
        gemini_schema = types.Schema(type=gemini_type)
        
        if "description" in schema:
            gemini_schema.description = schema["description"]
        
        if schema_type == "object" and "properties" in schema:
            properties = {}
            for prop_name, prop_schema in schema["properties"].items():
                properties[prop_name] = self._convert_json_schema_to_gemini_schema(prop_schema)
            gemini_schema.properties = properties
            
            if "required" in schema:
                gemini_schema.required = schema["required"]
        
        elif schema_type == "array" and "items" in schema:
            gemini_schema.items = self._convert_json_schema_to_gemini_schema(schema["items"])
        
        if "enum" in schema:
            gemini_schema.enum = schema["enum"]
        
        return gemini_schema

    async def run_async(self, *, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        ADK框架要求的异步运行方法
        :param args: 工具参数
        :param tool_context: 工具上下文
        :return: API调用结果
        """
        return await self.execute(**args)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """
        执行API调用
        :param kwargs: API调用参数
        :return: API响应结果
        """
        try:
            # 准备请求参数
            request_params = self._prepare_request_params(kwargs)
            
            # 执行异步HTTP请求
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.request(
                    method=self.method,
                    url=self.url,
                    **request_params
                ) as response:
                    # 获取响应内容
                    response_text = await response.text()
                    
                    # 尝试解析JSON响应
                    try:
                        response_data = json.loads(response_text)
                    except json.JSONDecodeError:
                        response_data = {"text": response_text}
                    
                    # 检查HTTP状态码
                    if response.status >= 400:
                        return {
                            "error": f"API调用失败，状态码: {response.status}",
                            "status_code": response.status,
                            "response": response_data
                        }
                    
                    return {
                        "success": True,
                        "status_code": response.status,
                        "data": response_data
                    }
                    
        except asyncio.TimeoutError:
            return {
                "error": "API调用超时",
                "timeout": self.timeout
            }
        except Exception as e:
            return {
                "error": f"API调用出现错误: {str(e)}",
                "details": {
                    "error_type": type(e).__name__,
                    "api_url": self.url,
                    "method": self.method
                }
            }

    def _prepare_request_params(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备HTTP请求参数
        :param kwargs: 输入参数
        :return: 请求参数字典
        """
        request_params = {}
        
        # 设置请求头
        if self.headers:
            request_params['headers'] = self.headers.copy()
        
        # 处理认证
        if self.auth:
            if self.auth.get('type') == 'bearer':
                if 'headers' not in request_params:
                    request_params['headers'] = {}
                request_params['headers']['Authorization'] = f"Bearer {self.auth['token']}"
            elif self.auth.get('type') == 'api_key':
                if 'headers' not in request_params:
                    request_params['headers'] = {}
                request_params['headers'][self.auth['header_name']] = self.auth['api_key']
        
        # 处理请求参数
        if self.method in ['GET', 'DELETE']:
            # GET和DELETE请求使用查询参数
            request_params['params'] = kwargs
        else:
            # POST、PUT、PATCH请求使用请求体
            if 'headers' not in request_params:
                request_params['headers'] = {}
            request_params['headers']['Content-Type'] = 'application/json'
            request_params['json'] = kwargs
        
        return request_params

class LLMWrappedFunctionTool(BaseTool):
    """
    LLM包装的网页API工具类：
    为网页API工具提供LLM驱动的智能调用能力
    """
    def __init__(self, 
                 function_tool: FunctionAPITool,
                 llm_manager: LLMManager,
                 llm_name: str,
                 instruction: str,
                 temperature: float = 0.3):
        """
        初始化LLM包装的网页API工具
        :param function_tool: 原始网页API工具实例
        :param llm_manager: LLM管理器实例
        :param llm_name: 要使用的LLM名称
        :param instruction: 系统指令/提示词
        :param temperature: 温度参数
        """
        super().__init__(name=function_tool.name, description=f"LLM包装的API工具: {function_tool.name}")
        self.function_tool = function_tool
        self.llm_manager = llm_manager
        self.llm_name = llm_name
        self.instruction = instruction
        self.temperature = temperature

    def _get_declaration(self):
        """
        获取工具声明，使用原始API工具的声明
        """
        return self.function_tool._get_declaration()

    def _build_prompt(self, **kwargs) -> str:
        """
        构建提示词
        :param kwargs: 提示词参数
        :return: 完整提示词
        """
        # 获取API工具的描述信息
        tool_declaration = self.function_tool._get_declaration()
        tool_info = f"API工具名称: {self.function_tool.name}\n"
        tool_info += f"API URL: {self.function_tool.url}\n"
        tool_info += f"HTTP方法: {self.function_tool.method}\n"
        if tool_declaration and hasattr(tool_declaration, 'description'):
            tool_info += f"工具描述: {tool_declaration.description}\n"
        if tool_declaration and hasattr(tool_declaration, 'parameters'):
            tool_info += f"工具参数: {tool_declaration.parameters}\n"
        
        prompt = f"{self.instruction}\n\n{tool_info}\n"
        for key, value in kwargs.items():
            prompt += f"{key}: {value}\n"
        prompt += "\n请根据上述信息和用户需求，生成调用该API工具所需的参数，以JSON格式返回。"
        return prompt

    async def run_async(self, *, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        ADK框架要求的异步运行方法
        :param args: LLM填充的参数
        :param tool_context: 工具上下文
        :return: 工具执行结果
        """
        return await self.execute(tool_context=tool_context, **args)

    async def execute(self, tool_context: Optional[ToolContext] = None, **kwargs) -> Dict[str, Any]:
        """
        执行LLM驱动的API工具调用
        :param tool_context: 工具上下文
        :param kwargs: 执行参数
        :return: 执行结果
        """
        try:
            # 构建消息
            messages = [
                {"role": "user", "content": self._build_prompt(**kwargs)}
            ]
            
            # 调用LLM获取工具参数
            response = self.llm_manager.chat(
                self.llm_name,
                messages=messages,
                temperature=self.temperature,
                stream=True
            )
            
            # 处理LLM响应（流式和非流式）
            result = ""
            if hasattr(response, '__iter__') and not isinstance(response, (dict, str)):
                # 流式响应处理
                chunk_count = 0
                finish_reason = None
                
                try:
                    for chunk in response:
                        chunk_count += 1
                        
                        if hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0:
                            choice = chunk.choices[0]
                            
                            if hasattr(choice, 'finish_reason') and choice.finish_reason:
                                finish_reason = choice.finish_reason
                                if finish_reason in ['stop', 'length', 'content_filter']:
                                    break
                            
                            if hasattr(choice, 'delta') and choice.delta:
                                delta = choice.delta
                                if hasattr(delta, 'content') and delta.content:
                                    result += delta.content
                                elif hasattr(delta, 'get'):
                                    content = delta.get("content", "")
                                    if content:
                                        result += content
                        
                        if chunk_count > 10000:
                            print(f"警告: 流式响应chunk数量超过限制，强制结束")
                            break
                    
                    if finish_reason is None and chunk_count > 0:
                        print(f"警告: 流式响应可能未正常结束，接收到{chunk_count}个chunk")
                    
                except Exception as stream_error:
                    print(f"流式响应处理出错: {str(stream_error)}")
                    if not result:
                        raise stream_error
                
                if not result.strip():
                    raise ValueError("流式响应返回空内容")
                    
            else:
                # 非流式响应处理
                if hasattr(response, 'choices') and response.choices:
                    result = response.choices[0].message.content.strip()
                else:
                    result = str(response).strip()
            
            # 解析LLM返回的参数
            try:
                tool_args = json.loads(result)
            except json.JSONDecodeError:
                # 如果LLM返回的不是JSON，尝试直接使用原始参数
                tool_args = kwargs
            
            # 调用原始API工具
            if tool_context:
                result = await self.function_tool.run_async(args=tool_args, tool_context=tool_context)
            else:
                # 创建一个临时的工具上下文
                from google.adk.tools.tool_context import ToolContext
                temp_context = ToolContext()
                result = await self.function_tool.run_async(args=tool_args, tool_context=temp_context)
            
            return result
                
        except Exception as e:
            return {
                "error": f"执行LLM包装的API工具时出现错误: {str(e)}",
                "details": {
                    "function_tool_name": self.function_tool.name,
                    "llm_name": self.llm_name,
                    "error_type": type(e).__name__
                }
            }

class LLM_FunctionTool(BaseTool):
    """
    LLM驱动的网页API工具类：
    结合LLM能力和网页API调用，实现智能API工具调用
    """
    def __init__(self,
                 name: str,
                 llm_manager: LLMManager,
                 llm_name: str,
                 instruction: str,
                 api_configs: List[Dict[str, Any]],
                 temperature: float = 0.3):
        """
        初始化LLM_FunctionTool
        :param name: 工具名称
        :param llm_manager: LLM管理器实例
        :param llm_name: 要使用的LLM名称
        :param instruction: 系统指令/提示词
        :param api_configs: API配置列表，每个配置包含name、description、api_config等
        :param temperature: 温度参数
        """
        super().__init__(name=name, description=f"LLM驱动的API工具集: {name}")
        self.llm_manager = llm_manager
        self.llm_name = llm_name
        self.instruction = instruction
        self.temperature = temperature
        self.api_configs = api_configs
        self.function_tools: List[FunctionAPITool] = []

        # 创建API工具实例
        self._create_function_tools()

    def _create_function_tools(self) -> None:
        """
        根据配置创建API工具实例
        """
        for config in self.api_configs:
            try:
                tool = FunctionAPITool(
                    name=config['name'],
                    description=config['description'],
                    api_config=config['api_config'],
                    json_schema=config.get('json_schema')
                )
                self.function_tools.append(tool)
            except Exception as e:
                print(f"创建API工具 {config.get('name', 'unknown')} 时出错: {str(e)}")

    def is_initialized(self) -> bool:
        """
        检查工具是否已初始化
        :return: 如果已初始化返回True，否则返回False
        """
        return len(self.function_tools) > 0

    async def run_async(self, *, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        ADK框架要求的异步运行方法
        注意：LLM_FunctionTool现在主要作为工具容器，实际执行由包装的工具完成
        :param args: 参数
        :param tool_context: 工具上下文
        :return: 执行结果
        """
        return {
            "message": "LLM_FunctionTool现在作为工具容器，请直接调用其包装的具体工具",
            "available_tools": [t.name for t in await self.get_tools(tool_context)]
        }

    async def get_tools(self, readonly_context: Optional[ToolContext] = None) -> Sequence[BaseTool]:
        """获取LLM包装的API工具列表"""
        if not self.is_initialized():
            return []

        # 为每个API工具创建LLM包装
        wrapped_tools = []
        for tool in self.function_tools:
            wrapped_tool = LLMWrappedFunctionTool(
                function_tool=tool,
                llm_manager=self.llm_manager,
                llm_name=self.llm_name,
                instruction=self.instruction,
                temperature=self.temperature
            )
            wrapped_tools.append(wrapped_tool)

        return wrapped_tools

    async def close(self) -> None:
        """关闭工具集，清理资源"""
        self.function_tools.clear()

class FunctionToolset(BaseToolset):
    """
    网页API工具集：
    管理一组网页API工具
    """
    def __init__(self, tools: List[Union[FunctionAPITool, LLMWrappedFunctionTool]]):
        """
        初始化工具集
        :param tools: API工具列表
        """
        super().__init__()
        self.tools = tools

    async def get_tools(self, readonly_context: Optional[ToolContext] = None) -> List[BaseTool]:
        """
        获取可用的工具列表
        :param readonly_context: 工具上下文
        :return: 工具列表
        """
        return self.tools

    async def close(self) -> None:
        """
        关闭工具集，清理资源
        """
        self.tools.clear()

class FunctionToolManager:
    """
    网页API工具管理器：
    提供工具创建、配置和管理的便捷方法
    """
    def __init__(self, llm_manager: LLMManager):
        """
        初始化工具管理器
        :param llm_manager: LLM管理器实例
        """
        self.llm_manager = llm_manager
        self.tools: Dict[str, Union[FunctionAPITool, LLMWrappedFunctionTool, LLM_FunctionTool]] = {}

    def create_api_tool(self,
                       name: str,
                       description: str,
                       api_config: Dict[str, Any],
                       json_schema: Optional[Dict[str, Any]] = None) -> FunctionAPITool:
        """
        创建单个API工具
        :param name: 工具名称
        :param description: 工具描述
        :param api_config: API配置
        :param json_schema: 参数schema
        :return: API工具实例
        """
        tool = FunctionAPITool(
            name=name,
            description=description,
            api_config=api_config,
            json_schema=json_schema
        )
        self.tools[name] = tool
        return tool

    def create_llm_wrapped_tool(self,
                               function_tool: FunctionAPITool,
                               llm_name: str,
                               instruction: str,
                               temperature: float = 0.3) -> LLMWrappedFunctionTool:
        """
        创建LLM包装的API工具
        :param function_tool: 原始API工具
        :param llm_name: LLM名称
        :param instruction: 指令
        :param temperature: 温度参数
        :return: LLM包装的工具实例
        """
        wrapped_tool = LLMWrappedFunctionTool(
            function_tool=function_tool,
            llm_manager=self.llm_manager,
            llm_name=llm_name,
            instruction=instruction,
            temperature=temperature
        )
        self.tools[f"llm_{function_tool.name}"] = wrapped_tool
        return wrapped_tool

    def create_llm_function_tool(self,
                                name: str,
                                llm_name: str,
                                instruction: str,
                                api_configs: List[Dict[str, Any]],
                                temperature: float = 0.3) -> LLM_FunctionTool:
        """
        创建LLM驱动的API工具集
        :param name: 工具集名称
        :param llm_name: LLM名称
        :param instruction: 指令
        :param api_configs: API配置列表
        :param temperature: 温度参数
        :return: LLM驱动的工具集实例
        """
        llm_tool = LLM_FunctionTool(
            name=name,
            llm_manager=self.llm_manager,
            llm_name=llm_name,
            instruction=instruction,
            api_configs=api_configs,
            temperature=temperature
        )
        self.tools[name] = llm_tool
        return llm_tool

    def get_tool(self, name: str) -> Optional[Union[FunctionAPITool, LLMWrappedFunctionTool, LLM_FunctionTool]]:
        """
        获取指定名称的工具
        :param name: 工具名称
        :return: 工具实例或None
        """
        return self.tools.get(name)

    def list_tools(self) -> List[str]:
        """
        列出所有工具名称
        :return: 工具名称列表
        """
        return list(self.tools.keys())

    def remove_tool(self, name: str) -> bool:
        """
        移除指定工具
        :param name: 工具名称
        :return: 是否成功移除
        """
        if name in self.tools:
            del self.tools[name]
            return True
        return False

    async def close_all(self) -> None:
        """
        关闭所有工具，清理资源
        """
        for tool in self.tools.values():
            if hasattr(tool, 'close'):
                await tool.close()
        self.tools.clear()
