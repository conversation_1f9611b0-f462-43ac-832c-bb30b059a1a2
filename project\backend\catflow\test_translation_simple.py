#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的翻译工具测试
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from base.tool_manager import ToolManager
from base.llm_manager import LLMManager

# 导入ADK相关类
try:
    from google.adk.agents.invocation_context import InvocationContext
    from google.adk.tools.tool_context import ToolContext
    from google.adk.sessions.in_memory_session_service import InMemorySessionService
    from google.adk.agents.sequential_agent import SequentialAgent
except ImportError:
    print("无法导入ADK模块，请确保ADK已正确安装")
    sys.exit(1)

async def test_translation():
    """简化的翻译测试"""
    print("=== 简化翻译工具测试 ===")
    
    try:
        # 创建LLM管理器和工具管理器
        llm_manager = LLMManager()
        tool_manager = ToolManager(llm_manager)
        
        # 获取翻译工具
        tools = await tool_manager.get_tools()
        translation_tool = None
        for tool in tools:
            if hasattr(tool, 'name') and 'translate' in tool.name.lower():
                translation_tool = tool
                break
        
        if not translation_tool:
            print("✗ 未找到翻译工具")
            return
        
        print(f"✓ 找到翻译工具: {translation_tool.name}")
        
        # 创建工具上下文
        session_service = InMemorySessionService()
        session = await session_service.create_session(app_name='test_app', user_id='test_user')
        agent = SequentialAgent(name='test_agent')
        invocation_context = InvocationContext(
            invocation_id='test_invocation',
            agent=agent,
            session=session,
            session_service=session_service,
        )
        tool_context = ToolContext(invocation_context)
        
        # 测试翻译
        test_cases = [
            {
                "text": "Hello, world!",
                "description": "英文翻译成中文"
            },
            {
                "text": "你好，世界！",
                "description": "中文翻译成英文"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_case['description']}")
            print(f"原文: {test_case['text']}")
            
            # 构造请求参数
            request_params = {
                "user_request": f"请翻译以下文本：{test_case['text']}"
            }
            
            # 调用翻译工具
            result = await translation_tool.run_async(args=request_params, tool_context=tool_context)
            
            if result and isinstance(result, dict):
                if result.get('success') and 'data' in result:
                    response_data = result['data'].get('responseData', {})
                    translated_text = response_data.get('translatedText', '')
                    if translated_text and 'NO QUERY SPECIFIED' not in translated_text:
                        print(f"✓ 翻译成功: {translated_text}")
                    else:
                        print(f"✗ 翻译失败: {translated_text}")
                else:
                    print(f"✗ API调用失败: {result}")
            else:
                print(f"✗ 返回结果格式错误: {result}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"✗ 测试出错: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_translation())
