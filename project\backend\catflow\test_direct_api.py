#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试MyMemory翻译API
"""

import asyncio
import aiohttp
import json

async def test_direct_api():
    """直接测试MyMemory API"""
    print("=== 直接测试MyMemory翻译API ===")
    
    # 测试参数
    test_cases = [
        {
            "q": "Hello, how are you?",
            "langpair": "en|zh",
            "description": "英文翻译成中文"
        },
        {
            "q": "你好，今天天气怎么样？",
            "langpair": "zh|en", 
            "description": "中文翻译成英文"
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_case['description']}")
            print(f"原文: {test_case['q']}")
            print(f"语言对: {test_case['langpair']}")
            
            try:
                # 构建请求URL
                url = "https://api.mymemory.translated.net/get"
                params = {
                    "q": test_case["q"],
                    "langpair": test_case["langpair"]
                }
                
                print(f"请求URL: {url}")
                print(f"请求参数: {params}")
                
                # 发送请求
                async with session.get(url, params=params) as response:
                    print(f"响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print("✓ API调用成功")
                        print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        
                        # 提取翻译结果
                        if 'responseData' in data and 'translatedText' in data['responseData']:
                            translated_text = data['responseData']['translatedText']
                            print(f"译文: {translated_text}")
                        else:
                            print("未找到翻译结果")
                    else:
                        print(f"✗ API调用失败，状态码: {response.status}")
                        text = await response.text()
                        print(f"错误响应: {text}")
                        
            except Exception as e:
                print(f"✗ 测试出错: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_direct_api())
