# LLM Function Tool 使用指南

## 概述

`llm_function_tool.py` 是一个强大的网页API工具模块，它基于与 `llm_mcp_tool.py` 相同的设计模式，提供了对网页API的智能调用能力。该模块结合了LLM的智能理解能力和HTTP API的实际功能，使得AI代理能够智能地调用各种网页服务。

## 核心特性

- **智能API调用**: 使用LLM理解用户意图并生成合适的API参数
- **多种认证方式**: 支持API Key、Bearer Token等认证方式
- **异步HTTP请求**: 基于aiohttp实现高性能的异步API调用
- **灵活的参数验证**: 支持JSON Schema定义API参数结构
- **错误处理**: 完善的错误处理和异常管理机制
- **工具管理**: 提供便捷的工具创建、配置和管理功能

## 架构设计

### 核心类结构

```
FunctionAPITool              # 基础API工具类
├── 直接HTTP API调用
├── 参数验证和处理
└── 响应解析和错误处理

LLMWrappedFunctionTool      # LLM包装的API工具类
├── 继承FunctionAPITool功能
├── LLM智能参数生成
└── 自然语言到API参数的转换

LLM_FunctionTool            # LLM驱动的API工具集
├── 管理多个API工具
├── 统一的LLM配置
└── 批量工具创建和管理

FunctionToolManager         # 工具管理器
├── 工具创建和配置
├── 工具生命周期管理
└── 资源清理和释放
```

## 快速开始

### 1. 基础API工具使用

```python
from base.llm_function_tool import FunctionAPITool

# 创建API配置
api_config = {
    "url": "https://api.example.com/data",
    "method": "GET",
    "headers": {
        "User-Agent": "MyApp/1.0"
    },
    "timeout": 10,
    "auth": {
        "type": "api_key",
        "header_name": "X-API-Key",
        "api_key": "your-api-key"
    }
}

# 定义参数schema
json_schema = {
    "type": "object",
    "properties": {
        "query": {
            "type": "string",
            "description": "搜索查询"
        }
    },
    "required": ["query"]
}

# 创建工具
tool = FunctionAPITool(
    name="search_api",
    description="搜索API工具",
    api_config=api_config,
    json_schema=json_schema
)

# 执行API调用
result = await tool.execute(query="python")
```

### 2. LLM包装工具使用

```python
from base.llm_function_tool import LLMWrappedFunctionTool
from base.llm_manager import LLMManager

# 创建LLM管理器
llm_manager = LLMManager()

# 创建LLM包装工具
wrapped_tool = LLMWrappedFunctionTool(
    function_tool=tool,  # 上面创建的基础工具
    llm_manager=llm_manager,
    llm_name="gemini-2.0-flash-exp",
    instruction="你是一个搜索助手，根据用户需求生成搜索参数",
    temperature=0.3
)

# 使用自然语言调用
result = await wrapped_tool.execute(user_request="帮我搜索Python教程")
```

### 3. 工具管理器使用

```python
from base.llm_function_tool import FunctionToolManager

# 创建工具管理器
tool_manager = FunctionToolManager(llm_manager)

# 创建API工具
api_tool = tool_manager.create_api_tool(
    name="weather_api",
    description="天气查询API",
    api_config=weather_api_config,
    json_schema=weather_schema
)

# 创建LLM包装工具
llm_tool = tool_manager.create_llm_wrapped_tool(
    function_tool=api_tool,
    llm_name="gemini-2.0-flash-exp",
    instruction="天气查询助手指令"
)

# 管理工具
tools = tool_manager.list_tools()
tool = tool_manager.get_tool("weather_api")
removed = tool_manager.remove_tool("weather_api")
```

## 配置说明

### API配置结构

```json
{
  "url": "https://api.example.com/endpoint",
  "method": "GET|POST|PUT|DELETE",
  "headers": {
    "User-Agent": "MyApp/1.0",
    "Content-Type": "application/json"
  },
  "timeout": 30,
  "auth": {
    "type": "api_key|bearer|basic",
    "header_name": "X-API-Key",
    "api_key": "your-key",
    "token": "bearer-token"
  }
}
```

### 认证方式

#### API Key认证
```json
{
  "auth": {
    "type": "api_key",
    "header_name": "X-API-Key",
    "api_key": "your-api-key"
  }
}
```

#### Bearer Token认证
```json
{
  "auth": {
    "type": "bearer",
    "token": "your-bearer-token"
  }
}
```

### JSON Schema定义

```json
{
  "type": "object",
  "properties": {
    "param1": {
      "type": "string",
      "description": "参数1描述"
    },
    "param2": {
      "type": "integer",
      "description": "参数2描述",
      "minimum": 1,
      "maximum": 100
    },
    "param3": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "字符串数组参数"
    }
  },
  "required": ["param1"]
}
```

## 实际应用示例

### 天气查询工具

```python
# 天气API配置
weather_config = {
    "url": "https://api.openweathermap.org/data/2.5/weather",
    "method": "GET",
    "auth": {
        "type": "api_key",
        "header_name": "appid",
        "api_key": "${OPENWEATHER_API_KEY}"
    }
}

weather_schema = {
    "type": "object",
    "properties": {
        "q": {
            "type": "string",
            "description": "城市名称"
        },
        "units": {
            "type": "string",
            "enum": ["metric", "imperial"],
            "default": "metric"
        }
    },
    "required": ["q"]
}

# 创建天气工具
weather_tool = FunctionAPITool(
    name="weather_query",
    description="查询城市天气信息",
    api_config=weather_config,
    json_schema=weather_schema
)
```

### 新闻搜索工具

```python
# 新闻API配置
news_config = {
    "url": "https://newsapi.org/v2/everything",
    "method": "GET",
    "auth": {
        "type": "api_key",
        "header_name": "X-API-Key",
        "api_key": "${NEWS_API_KEY}"
    }
}

news_schema = {
    "type": "object",
    "properties": {
        "q": {
            "type": "string",
            "description": "搜索关键词"
        },
        "language": {
            "type": "string",
            "enum": ["zh", "en"],
            "default": "zh"
        },
        "sortBy": {
            "type": "string",
            "enum": ["relevancy", "popularity", "publishedAt"],
            "default": "publishedAt"
        }
    },
    "required": ["q"]
}
```

## 错误处理

### 常见错误类型

1. **配置错误**: API配置缺少必需字段
2. **网络错误**: 连接超时、DNS解析失败
3. **HTTP错误**: 4xx、5xx状态码
4. **认证错误**: API密钥无效、权限不足
5. **数据错误**: JSON解析失败、参数验证失败

### 错误响应格式

```json
{
  "error": "错误描述信息",
  "details": {
    "error_type": "错误类型",
    "api_url": "API地址",
    "method": "HTTP方法",
    "status_code": 400
  }
}
```

## 最佳实践

### 1. 安全性
- 使用环境变量存储API密钥
- 设置合理的超时时间
- 验证API响应数据

### 2. 性能优化
- 使用异步调用避免阻塞
- 设置适当的并发限制
- 缓存频繁使用的数据

### 3. 错误处理
- 实现重试机制
- 记录详细的错误日志
- 提供友好的错误信息

### 4. 配置管理
- 使用配置文件管理API设置
- 支持环境变量替换
- 版本化API配置

## 测试和调试

### 运行测试
```bash
# 基础功能测试
python test_llm_function_tool.py

# 使用示例
python function_tool_usage_example.py
```

### 调试技巧
1. 启用详细日志记录
2. 使用httpbin.org进行API测试
3. 检查网络连接和防火墙设置
4. 验证API密钥和权限

## 扩展和定制

### 添加新的认证方式
```python
def _prepare_request_params(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
    # 在此方法中添加新的认证逻辑
    if self.auth and self.auth.get('type') == 'custom':
        # 实现自定义认证逻辑
        pass
```

### 自定义响应处理
```python
async def execute(self, **kwargs) -> Dict[str, Any]:
    # 在此方法中添加自定义响应处理逻辑
    response_data = await self._make_request(kwargs)
    # 自定义处理逻辑
    return processed_data
```

## 注意事项

1. **API限制**: 注意第三方API的调用频率限制
2. **数据隐私**: 确保敏感数据的安全传输
3. **版本兼容**: 关注API版本变更和兼容性
4. **资源管理**: 及时关闭连接和清理资源

## 相关文档

- [LLM Manager 使用指南](./LLM_Manager_README.md)
- [MCP Tool 使用指南](./MCP_Tool_README.md)
- [工具配置示例](../function_tool_config_example.json)
- [使用示例代码](../function_tool_usage_example.py)
