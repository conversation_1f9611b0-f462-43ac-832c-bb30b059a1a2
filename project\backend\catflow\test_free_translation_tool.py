#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
免费翻译API工具测试

测试通过tool_manager调用免费翻译API工具的功能
"""

import asyncio
import json
import os
import sys
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from base.tool_manager import ToolManager
from base.llm_manager import LLMManager

# 导入ADK相关类
try:
    from google.adk.agents.invocation_context import InvocationContext
    from google.adk.tools.tool_context import ToolContext
    from google.adk.sessions.in_memory_session_service import InMemorySessionService
    from google.adk.agents.sequential_agent import SequentialAgent
except ImportError:
    print("无法导入ADK模块，请确保ADK已正确安装")
    sys.exit(1)

class FreeTranslationToolTest:
    """免费翻译API工具测试类"""

    def __init__(self):
        """初始化测试类"""
        self.llm_manager = LLMManager()
        self.tool_manager = None
        self.translation_tool = None
        self.tool_context = None
    
    async def setup(self):
        """设置测试环境"""
        print("=== 设置测试环境 ===")
        
        try:
            # 创建工具管理器
            config_path = os.path.join(os.path.dirname(__file__), 'tool_config.json')
            self.tool_manager = ToolManager(
                llm_manager=self.llm_manager,
                config_path=config_path
            )
            print("✓ 成功创建工具管理器")
            
            # 获取所有工具
            tools = await self.tool_manager.get_tools()
            print(f"✓ 加载了 {len(tools)} 个工具")
            
            # 查找翻译工具
            for tool in tools:
                if 'translation' in tool.name.lower() or 'translate' in tool.name.lower():
                    self.translation_tool = tool
                    break
            
            if self.translation_tool:
                print(f"✓ 找到翻译工具: {self.translation_tool.name}")
                print(f"  描述: {self.translation_tool.description}")
            else:
                print("✗ 未找到翻译工具")
                return False

            # 创建ToolContext
            await self._create_tool_context()

            return True
            
        except Exception as e:
            print(f"✗ 设置测试环境时出错: {str(e)}")
            return False

    async def _create_tool_context(self):
        """创建工具上下文"""
        try:
            # 创建会话服务
            session_service = InMemorySessionService()
            session = await session_service.create_session(
                app_name='test_app',
                user_id='test_user'
            )

            # 创建代理
            agent = SequentialAgent(name='test_agent')

            # 创建调用上下文
            invocation_context = InvocationContext(
                invocation_id='test_invocation',
                agent=agent,
                session=session,
                session_service=session_service,
            )

            # 创建工具上下文
            self.tool_context = ToolContext(invocation_context)
            print("✓ 成功创建工具上下文")

        except Exception as e:
            print(f"✗ 创建工具上下文时出错: {str(e)}")
            raise
    
    async def test_tool_declaration(self):
        """测试工具声明"""
        print("\n=== 测试工具声明 ===")
        
        if not self.translation_tool:
            print("✗ 翻译工具未初始化")
            return
        
        try:
            # 获取工具声明
            declaration = self.translation_tool._get_declaration()
            if declaration:
                print(f"✓ 工具声明获取成功")
                print(f"  工具名称: {declaration.name}")
                print(f"  工具描述: {declaration.description}")
                
                # 检查输入schema
                if hasattr(declaration, 'input_schema') and declaration.input_schema:
                    print(f"  输入schema: {declaration.input_schema}")
                else:
                    print("  输入schema: 未定义")
            else:
                print("✗ 获取工具声明失败")
                
        except Exception as e:
            print(f"✗ 测试工具声明时出错: {str(e)}")
    
    async def test_english_to_chinese(self):
        """测试英文翻译成中文"""
        print("\n=== 测试英文翻译成中文 ===")
        
        if not self.translation_tool:
            print("✗ 翻译工具未初始化")
            return
        
        try:
            # 测试文本
            test_text = "Hello, how are you today? I hope you have a wonderful day!"
            print(f"原文: {test_text}")
            
            # 构造请求参数
            request_params = {
                "user_request": f"请将以下英文翻译成中文：{test_text}"
            }

            print("正在调用翻译工具...")

            # 调用翻译工具 - 使用预先创建的tool_context
            result = await self.translation_tool.run_async(args=request_params, tool_context=self.tool_context)
            
            if result:
                print("✓ 翻译调用成功")
                print(f"翻译结果: {result}")
                
                # 尝试解析结果
                if isinstance(result, dict):
                    if 'translated_text' in result:
                        print(f"译文: {result['translated_text']}")
                    elif 'responseData' in result:
                        translated_text = result['responseData'].get('translatedText', '')
                        print(f"译文: {translated_text}")
                    else:
                        print(f"完整结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                else:
                    print(f"结果类型: {type(result)}")
                    print(f"结果内容: {result}")
            else:
                print("✗ 翻译调用失败，返回空结果")
                
        except Exception as e:
            print(f"✗ 测试英文翻译时出错: {str(e)}")
    
    async def test_chinese_to_english(self):
        """测试中文翻译成英文"""
        print("\n=== 测试中文翻译成英文 ===")
        
        if not self.translation_tool:
            print("✗ 翻译工具未初始化")
            return
        
        try:
            # 测试文本
            test_text = "你好，今天天气怎么样？希望你有美好的一天！"
            print(f"原文: {test_text}")
            
            # 构造请求参数
            request_params = {
                "user_request": f"请将以下中文翻译成英文：{test_text}"
            }

            print("正在调用翻译工具...")

            # 调用翻译工具 - 使用预先创建的tool_context
            result = await self.translation_tool.run_async(args=request_params, tool_context=self.tool_context)
            
            if result:
                print("✓ 翻译调用成功")
                
                # 尝试解析结果
                if isinstance(result, dict):
                    if 'translated_text' in result:
                        print(f"译文: {result['translated_text']}")
                    elif 'responseData' in result:
                        translated_text = result['responseData'].get('translatedText', '')
                        print(f"译文: {translated_text}")
                    else:
                        print(f"完整结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                else:
                    print(f"结果内容: {result}")
            else:
                print("✗ 翻译调用失败，返回空结果")
                
        except Exception as e:
            print(f"✗ 测试中文翻译时出错: {str(e)}")
    
    async def test_japanese_to_chinese(self):
        """测试日文翻译成中文"""
        print("\n=== 测试日文翻译成中文 ===")
        
        if not self.translation_tool:
            print("✗ 翻译工具未初始化")
            return
        
        try:
            # 测试文本
            test_text = "こんにちは、元気ですか？素晴らしい一日をお過ごしください！"
            print(f"原文: {test_text}")
            
            # 构造请求参数
            request_params = {
                "user_request": f"请将以下日文翻译成中文：{test_text}"
            }

            print("正在调用翻译工具...")

            # 调用翻译工具 - 使用预先创建的tool_context
            result = await self.translation_tool.run_async(args=request_params, tool_context=self.tool_context)
            
            if result:
                print("✓ 翻译调用成功")
                
                # 尝试解析结果
                if isinstance(result, dict):
                    if 'translated_text' in result:
                        print(f"译文: {result['translated_text']}")
                    elif 'responseData' in result:
                        translated_text = result['responseData'].get('translatedText', '')
                        print(f"译文: {translated_text}")
                    else:
                        print(f"完整结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                else:
                    print(f"结果内容: {result}")
            else:
                print("✗ 翻译调用失败，返回空结果")
                
        except Exception as e:
            print(f"✗ 测试日文翻译时出错: {str(e)}")
    
    async def test_auto_detect_language(self):
        """测试自动检测语言"""
        print("\n=== 测试自动检测语言 ===")
        
        if not self.translation_tool:
            print("✗ 翻译工具未初始化")
            return
        
        try:
            # 测试多种语言的文本
            test_cases = [
                "Bonjour, comment allez-vous?",  # 法语
                "Hola, ¿cómo estás?",           # 西班牙语
                "Guten Tag, wie geht es Ihnen?", # 德语
            ]
            
            for i, test_text in enumerate(test_cases, 1):
                print(f"\n测试案例 {i}:")
                print(f"原文: {test_text}")
                
                # 构造请求参数
                request_params = {
                    "user_request": f"请将以下文本翻译成中文：{test_text}"
                }

                print("正在调用翻译工具...")

                # 调用翻译工具 - 使用预先创建的tool_context
                result = await self.translation_tool.run_async(args=request_params, tool_context=self.tool_context)
                
                if result:
                    print("✓ 翻译调用成功")
                    
                    # 尝试解析结果
                    if isinstance(result, dict):
                        if 'translated_text' in result:
                            print(f"译文: {result['translated_text']}")
                        elif 'responseData' in result:
                            translated_text = result['responseData'].get('translatedText', '')
                            print(f"译文: {translated_text}")
                        else:
                            print(f"完整结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    else:
                        print(f"结果内容: {result}")
                else:
                    print("✗ 翻译调用失败，返回空结果")
                
                # 添加延迟避免API限制
                await asyncio.sleep(1)
                
        except Exception as e:
            print(f"✗ 测试自动检测语言时出错: {str(e)}")
    
    async def cleanup(self):
        """清理测试环境"""
        print("\n=== 清理测试环境 ===")
        
        try:
            if self.tool_manager:
                await self.tool_manager.close()
                print("✓ 工具管理器已关闭")
            
        except Exception as e:
            print(f"✗ 清理测试环境时出错: {str(e)}")

async def main():
    """主测试函数"""
    print("免费翻译API工具测试")
    print("=" * 50)
    
    test = FreeTranslationToolTest()
    
    try:
        # 设置测试环境
        if not await test.setup():
            print("测试环境设置失败，退出测试")
            return
        
        # 运行各项测试
        await test.test_tool_declaration()
        await test.test_english_to_chinese()
        await test.test_chinese_to_english()
        await test.test_japanese_to_chinese()
        await test.test_auto_detect_language()
        
    except Exception as e:
        print(f"运行测试时出错: {str(e)}")
    
    finally:
        # 清理测试环境
        await test.cleanup()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())
