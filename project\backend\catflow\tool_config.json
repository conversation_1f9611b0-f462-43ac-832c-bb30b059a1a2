{"meta": {"description": "工具配置文件", "purpose": "定义系统中可用的工具及其配置信息", "structure": {"tools": {"type": "object", "description": "工具配置对象，包含所有可用工具的定义", "properties": {"[tool_name]": {"type": "object", "description": "单个工具的配置", "properties": {"name": {"type": "string", "description": "工具名称，必须唯一", "required": true}, "descriptions": {"type": "object", "description": "工具描述信息", "properties": {"tool": {"type": "string", "description": "工具的简短描述"}, "summary": {"type": "string", "description": "工具功能的详细说明"}, "parameters": {"type": "object", "description": "工具参数说明"}, "returns": {"type": "object|array", "description": "工具返回值说明"}}}, "type": {"type": "string", "description": "工具类型", "required": true, "enum": ["llm", "mcp", "function", "api"]}, "llm_name": {"type": "string", "description": "使用的语言模型名称，需在llm_config.json中定义", "required": true}, "instruction": {"type": "string", "description": "工具执行指令，指导工具如何处理请求", "required": true}, "temperature": {"type": "number", "description": "生成温度，仅适用于llm类型工具", "range": "0.0-1.0", "required": false}, "json_schema": {"type": "object", "description": "JSON输出格式定义，仅适用于llm类型工具", "required": false}, "connection_type": {"type": "string", "description": "连接类型，仅适用于mcp类型工具", "enum": ["stdio", "sse"], "required": false}, "connection_params": {"type": "object", "description": "连接参数配置，仅适用于mcp类型工具", "properties": {"mcpServers": {"type": "object", "description": "MCP服务器配置"}, "timeout": {"type": "number", "description": "连接超时时间（秒）"}, "sse_read_timeout": {"type": "number", "description": "SSE读取超时时间（秒），仅适用于sse连接类型"}}, "required": false}}}}}}, "usage": {"description": "使用方法", "steps": ["1. 在tools对象中定义新的工具", "2. 设置工具的基本属性：name、type、llm_name", "3. 配置descriptions提供详细的工具说明", "4. 根据工具类型设置相应的配置参数", "5. 在agent_config.json中的tools数组中引用工具名称"], "tool_types": {"llm": {"description": "基于语言模型的工具，通过LLM处理请求并返回结果", "required_fields": ["instruction", "json_schema"], "optional_fields": ["temperature"]}, "mcp": {"description": "Model Context Protocol工具，通过MCP协议连接外部服务", "required_fields": ["connection_type", "connection_params"], "connection_types": {"stdio": "标准输入输出连接", "sse": "Server-Sent Events连接"}}, "function": {"description": "函数工具，直接调用Python函数", "required_fields": ["function_path"]}, "api": {"description": "API工具，通过HTTP请求调用外部API", "required_fields": ["api_url", "method"]}}, "examples": {"llm_tool": {"name": "example_llm_tool", "type": "llm", "llm_name": "qwen-max", "instruction": "你是一个示例工具", "temperature": 0.7, "json_schema": {"result": "string"}}, "mcp_tool": {"name": "example_mcp_tool", "type": "mcp", "llm_name": "qwen-max", "instruction": "你是一个MCP工具", "connection_type": "stdio", "connection_params": {"mcpServers": {"example": {"command": "python", "args": ["-m", "example_server"]}}, "timeout": 10.0}}}, "best_practices": ["为工具提供清晰详细的描述信息", "合理设置超时时间避免长时间等待", "使用描述性的工具名称", "为LLM工具定义明确的JSON输出格式", "测试工具配置确保连接正常"]}}, "tools": {"weather_query": {"name": "weather_query", "descriptions": {"tool": "天气查询工具", "summary": "查询指定城市的天气信息，包括温度、天气状况、湿度等", "parameters": {"city": "要查询天气的城市名称"}, "returns": {"location": "城市名称", "temperature": "温度（摄氏度）", "weather": "天气状况", "humidity": "湿度（百分比）", "wind": "风向和风力", "suggestion": "活动建议"}}, "type": "llm", "llm_name": "qwen-max", "instruction": "你是一个天气助手，请根据用户提供的城市返回天气信息，只输出JSON字符串，不包含其他信息，如果没有查询到信息则模拟对应的JSON值返回", "temperature": 0.7, "json_schema": {"location": "string", "temperature": "number", "weather": "string", "humidity": "number", "wind": "string", "suggestion": "string"}}, "restaurant_recommend": {"name": "restaurant_recommend", "descriptions": {"tool": "餐厅推荐工具", "summary": "根据用户的偏好推荐餐厅", "parameters": {"cuisine": "菜系", "location": "位置", "price_range": "价格区间", "pagesize": "推荐数量"}, "returns": [{"name": "餐厅名称", "cuisine": "菜系", "location": "位置", "price_range": "价格区间", "rating": "评分", "recommendation": "推荐理由", "menu": "菜品推荐"}]}, "type": "llm", "llm_name": "qwen-max", "instruction": "你是一个餐厅推荐助手，注意位置要返回具体地址，价格要有一个人均价位，注意所有参数是否都已添加，尤其是推荐数量参数，根据用户的偏好推荐合适条数的餐厅，只输出JSON字符串，不包含其他信息。", "temperature": 0.7, "json_schema": {"name": "string", "cuisine": "string", "location": "string", "price_range": "string", "rating": "number", "recommendation": "string", "menu": "string"}}, "hotel_recommend": {"name": "hotel_recommend", "descriptions": {"tool": "住宿推荐工具", "summary": "根据用户的偏好推荐住宿，位置要提供详细的地址，价格要有一个区间，注意所有参数是否都已添加，尤其是推荐数量参数，根据用户的偏好推荐合适条数的住宿。", "parameters": {"location": "位置", "price_range": "价格区间", "pagesize": "推荐数量"}, "returns": [{"name": "住宿名称", "location": "位置", "price_range": "价格区间", "rating": "评分", "recommendation": "推荐理由", "menu": "房间介绍"}]}, "type": "llm", "llm_name": "qwen-max", "instruction": "你是一个住宿助手，根据用户的偏好推荐住宿，位置要提供详细的地址，价格要有一个区间，注意所有参数是否都已添加，尤其是推荐数量参数，根据用户的偏好推荐合适条数的住宿，只输出JSON字符串，不包含其他信息。", "temperature": 0.7, "json_schema": {"name": "string", "location": "string", "price_range": "string", "rating": "number", "recommendation": "string", "menu": "string"}}, "fetch_tool": {"name": "fetch_tool", "type": "mcp", "llm_name": "qwen-max", "instruction": "你是一个数据获取助手，请根据用户提供的URL获取网页内容。", "connection_type": "stdio", "connection_params": {"mcpServers": {"fetch": {"command": "python", "args": ["-m", "mcp_server_fetch"], "env": {}}}, "timeout": 10.0}}, "Filesystem": {"name": "filesystem", "type": "mcp", "llm_name": "qwen-max", "instruction": "你是一个文件助手，请根据用户提供的文件路径读取，文件内容创建、删除、修改。", "connection_type": "stdio", "connection_params": {"mcpServers": {"Filesystem": {"command": "D:\\Application\\NodeJS\\npx.cmd", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:\\Ready\\Adk\\reporting"], "env": {"NODE_ENV": "production"}}}, "timeout": 30.0}}, "bing_search": {"name": "bing_search", "type": "mcp", "llm_name": "qwen-max", "instruction": "你是一个搜索助手，请根据用户提供的关键词使用必应搜索引擎搜索信息。", "connection_type": "sse", "connection_params": {"mcpServers": {"bing_search": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/683b27a5e4ef4c/sse", "headers": {"Authorization": "Bearer 19d0d19a-cafc-4ca3-a71c-facb53a8323b"}}}, "timeout": 5.0, "sse_read_timeout": 300.0}}, "time": {"name": "time", "type": "mcp", "llm_name": "qwen-max", "instruction": "你是一个时间助手，请根据用户提供的时间和格式转换指令，返回正确的时间格式。", "connection_type": "stdio", "connection_params": {"mcpServers": {"time": {"command": "python", "args": ["-m", "mcp_server_time"], "env": {}}}, "timeout": 5.0}}, "free_translation_api": {"name": "free_translation_api", "descriptions": {"tool": "免费翻译API工具", "summary": "使用MyMemory免费翻译API进行文本翻译，支持多种语言互译", "parameters": {"text": "要翻译的文本内容", "source_lang": "源语言代码（如：en、zh、ja等）", "target_lang": "目标语言代码（如：en、zh、ja等）"}, "returns": {"original_text": "原始文本", "translated_text": "翻译后的文本", "source_language": "源语言", "target_language": "目标语言", "translation_quality": "翻译质量评分"}}, "type": "function", "llm_name": "qwen-max", "instruction": "你是一个翻译助手，根据用户提供的文本和语言要求，生成合适的翻译API调用参数。请识别源语言和目标语言，并生成正确的语言对格式。如果用户没有指定源语言，请自动检测。常用语言代码：中文(zh)、英文(en)、日文(ja)、韩文(ko)、法文(fr)、德文(de)、西班牙文(es)等。", "temperature": 0.2, "api_configs": [{"name": "translate_text", "description": "翻译文本内容", "api_config": {"url": "https://api.mymemory.translated.net/get", "method": "GET", "headers": {"User-Agent": "CatFlow-Translate/1.0"}, "timeout": 15}, "json_schema": {"type": "object", "properties": {"q": {"type": "string", "description": "要翻译的文本内容"}, "langpair": {"type": "string", "description": "语言对，格式：源语言|目标语言，如：en|zh、zh|en、ja|zh等", "default": "en|zh"}}, "required": ["q", "langpair"]}}]}}}